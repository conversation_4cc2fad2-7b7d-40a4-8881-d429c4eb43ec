'use client';
import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Badge, Divide<PERSON>, Toolt<PERSON> } from 'antd';
import { CheckOutlined, StarFilled, CrownOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import clsx from 'clsx';

// 订阅计划数据
const plans = [
  {
    id: 'starter',
    name: '尝鲜一下',
    price: 3,
    credits: 1500000,
    isPopular: false,
    bonus: null,
    icon: <StarFilled className="text-blue-500" />,
    buttonType: 'default' as const,
    estimate: [
      'GPT 4o mini: 约 1800 条',
      'GPT 4.1: 约 140 条',
      'Gemini 2.5 Pro: 约 130 条',
    ],
    basicFeatures: [
      'GPT 4.1',
      'GPT 4o',
      'GPT o4 mini',
      'Claude Sonnet 4',
      'Claude Opus 4',
      'Gemini 2.5 Pro',
      'Gemini 2.5 Flash'
    ],
    advancedFeatures: [
      'MCP 支持',
      '自定义 API Key 接入',
      '团队配额资源共享'
    ]
  },
  {
    id: 'light',
    name: '轻度使用',
    price: 10,
    credits: 6000000,
    isPopular: true,
    bonus: '20% 赠送',
    icon: <CrownOutlined className="text-yellow-500" />,
    buttonType: 'primary' as const,
    estimate: [
      'GPT 4o mini: 约 7200 条',
      'GPT 4.1: 约 560 条',
      'Gemini 2.5 Pro: 约 520 条',
    ],
    basicFeatures: [
      'GPT 4.1',
      'GPT 4o',
      'GPT o4 mini',
      'Claude Sonnet 4',
      'Claude Opus 4',
      'Gemini 2.5 Pro',
      'Gemini 2.5 Flash'
    ],
    advancedFeatures: [
      'MCP 支持',
      '自定义 API Key 接入',
      '团队配额资源共享'
    ]
  },
  {
    id: 'enterprise',
    name: '企业多人使用',
    price: 100,
    credits: 80000000,
    isPopular: false,
    bonus: null,
    icon: <CrownOutlined className="text-purple-500" />,
    buttonType: 'default' as const,

    estimate: [
      'GPT 4o mini: 约 96000 条',
      'GPT 4.1: 约 7500 条',
      'Gemini 2.5 Pro: 约 7000 条',
    ],
    basicFeatures: [
      'GPT 4.1',
      'GPT 4o',
      'GPT o4 mini',
      'Claude Sonnet 4',
      'Claude Opus 4',
      'Gemini 2.5 Pro',
      'Gemini 2.5 Flash'
    ],
    advancedFeatures: [
      'MCP 支持',
      '自定义 API Key 接入',
      '团队配额资源共享'
    ]
  }
];

const PlansPage = () => {
  const formatCredits = (credits: number) => {
    return credits.toLocaleString();
  };

  const handlePurchase = (planId: string) => {
    // 暂时只显示样式，不实现实际逻辑
    console.log(`购买计划: ${planId}`);
  };

  return (
    <div className="container max-w-6xl mx-auto p-4 md:p-8">
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">
          按量付费，无需订阅
        </h1>
      </div>

      {/* 计划卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className={clsx(
              "relative transition-all duration-300 hover:shadow-xl",
              plan.isPopular
                ? "border-2 border-blue-500 shadow-lg"
                : "border border-gray-200 hover:border-blue-300"
            )}
            styles={{
              body: {
                padding: '24px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
              }
            }}
          >
            {/* 推荐标签 */}
            {plan.isPopular && (
              <Badge.Ribbon
                style={{ marginTop: -22 }}
                text="最受欢迎，20% 赠送" color="blue" />
            )}

            {/* 计划头部 */}
            <div className="text-center mt-6 mb-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {plan.name}
              </h3>
              {/* 价格 */}
              <div className="mb-4">
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-gray-900">
                    ${plan.price}
                  </span>
                </div>
              </div>

              {/* 积分信息 */}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600 mb-1">一次性获得</div>
                <div className="text-lg font-bold text-blue-600">
                  {formatCredits(plan.credits)} 积分
                </div>
              </div>
            </div>

            {/* 功能列表 */}
            <div className="flex-1">
              <Divider orientation="left" orientationMargin="0">
                <span className="text-sm font-medium text-gray-700">使用预估</span>
                <Tooltip title="按平均每条消息消耗 2,500 Token 估算">
                  <QuestionCircleOutlined className='text-sm ml-2 text-gray-500' />
                </Tooltip>
              </Divider>
              <ul className="space-y-1 mb-2">
                {plan.estimate.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckOutlined className="text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <span className="text-sm text-gray-700 leading-relaxed">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>
              <span className='ml-7 text-xs text-gray-400'> 详情见计价说明文档</span>
              {/* 基础功能 */}
              <Divider orientation="left" orientationMargin="0">
                <span className="text-sm font-medium text-gray-700">所有大模型对话</span>
              </Divider>
              <ul className="space-y-1 mb-4">
                {plan.basicFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckOutlined className="text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <span className="text-sm text-gray-700 leading-relaxed">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              {/* 高级功能 */}
              <Divider orientation="left" orientationMargin="0">
                <span className="text-sm font-medium text-gray-700">高级功能</span>
              </Divider>
              <ul className="space-y-1">
                {plan.advancedFeatures.map((feature, index) => (
                  <li key={`advanced-${index}`} className="flex items-start">
                    <CheckOutlined className="text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <span className="text-sm text-gray-700 leading-relaxed">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {/* 购买按钮 */}
            <div className="mt-8">
              <Button
                type={plan.buttonType}
                size="large"
                block
                className={clsx(
                  "h-12 font-medium transition-all duration-200",
                  plan.isPopular
                    ? "shadow-lg hover:shadow-xl"
                    : "hover:shadow-md"
                )}
                onClick={() => handlePurchase(plan.id)}
              >
                立即购买
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* 底部说明 */}
      <div className="mt-12 text-center">
        <div className="bg-blue-50 rounded-lg p-6 max-w-4xl mx-auto">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            购买说明
          </h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p>• 所有计划均为一次性购买，积分永久有效</p>
            <p>• 支持所有主流 AI 模型，按实际使用量扣除积分</p>
            <p>• 企业版支持团队成员共享积分配额</p>
            <p>• 如有疑问，请联系客服获取帮助</p>
          </div>
        </div>
      </div>
      <div className='h-4'></div>
      <div>
        <form action="/api/hello5/checkout_sessions" method="POST">
          <section>
            <button type="submit" role="link">
              Checkout
            </button>
          </section>
        </form>
      </div>
    </div>
  );
};

export default PlansPage;