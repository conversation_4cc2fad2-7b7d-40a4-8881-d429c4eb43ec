'use client';

import React, { useState, useEffect } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, Spin, Alert } from 'antd';
import { getCreditConsumptionStats, DailyCreditConsumption } from './actions';

interface CreditConsumptionChartProps {
  workspaceId: string;
}

const CreditConsumptionChart: React.FC<CreditConsumptionChartProps> = ({ workspaceId }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DailyCreditConsumption[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getCreditConsumptionStats(workspaceId);
      if (result.status === 'success' && result.data) {
        setData(result.data.data);
      } else {
        setError(result.message || '获取数据失败');
      }
    } catch (err) {
      setError('获取数据时发生错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (workspaceId) {
      loadData();
    }
  }, [workspaceId]);

  // Y轴格式化函数
  const formatYAxisLabel = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded shadow-lg">
          <p className="text-gray-600 text-sm mb-1">{`日期: ${label}`}</p>
          <p className="text-blue-600 font-medium">
            {`积分消耗: ${payload[0].value?.toLocaleString() || 0} 积分`}
          </p>
        </div>
      );
    }
    return null;
  };

  // X轴标签过滤函数
  const formatXAxisLabel = (tickItem: string, index: number) => {
    // 根据数据长度动态调整显示间隔
    const interval = Math.max(1, Math.ceil(data.length / 15));
    return index % interval === 0 ? tickItem : '';
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={loadData}
              className="text-blue-500 hover:text-blue-700 underline"
            >
              重试
            </button>
          }
        />
      </Card>
    );
  }

  return (
    <Card
      title="最近30天积分消耗统计"
      className="mb-6"
      extra={
        <button
          onClick={loadData}
          className="text-blue-500 hover:text-blue-700 text-sm"
        >
          刷新
        </button>
      }
    >
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 20,
            left: 0,
            bottom: 0,
          }}
          barCategoryGap="20%"
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            tickFormatter={formatXAxisLabel}
            interval={0}
            axisLine={{ stroke: '#d9d9d9', strokeWidth: 1 }}
            tickLine={{ stroke: '#d9d9d9', strokeWidth: 1 }}
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={formatYAxisLabel}
            axisLine={{ stroke: '#d9d9d9', strokeWidth: 1 }}
            tickLine={{ stroke: '#d9d9d9', strokeWidth: 1 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="totalCredits"
            fill="#1890ff"
            radius={[2, 2, 0, 0]}
            maxBarSize={60}
          />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default CreditConsumptionChart;
